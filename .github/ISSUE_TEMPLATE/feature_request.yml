# This template is heavily inspired by the Next.js's template:
# See here: https://github.com/vercel/next.js/tree/canary/.github/ISSUE_TEMPLATE

name: 🛠 Feature Request
description: Create a feature request for the core packages
title: "feat: "
labels: ["✨ enhancement"]
body:
  - type: markdown
    attributes:
      value: |
        Thank you for taking the time to file a feature request. Please fill out this form as completely as possible.
  - type: textarea
    attributes:
      label: Describe the feature you'd like to request
      description: Please describe the feature as clear and concise as possible. Remember to add context as to why you believe this feature is needed.
    validations:
      required: true
  - type: textarea
    attributes:
      label: Describe the solution you'd like to see
      description: Please describe the solution you would like to see. Adding example usage is a good way to provide context.
    validations:
      required: true
  - type: textarea
    attributes:
      label: Additional information
      description: Add any other information related to the feature here. If your feature request is related to any issues or discussions, link them here.
