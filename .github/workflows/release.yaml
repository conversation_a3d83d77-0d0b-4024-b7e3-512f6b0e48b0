# This workflow will release the packages with Changesets

name: 🚀 Release

on:
  push:
    branches:
      - main
  workflow_dispatch:

concurrency: ${{ github.workflow }}-${{ github.ref }}

env:
  GITHUB_TOKEN: ${{ secrets.GH_TOKEN }}
  NPM_TOKEN: ${{ secrets.NPM_TOKEN }}

jobs:
  release:
    name: 🚀 Release
    strategy:
      matrix:
        os: [ubuntu-latest]
        node-version: [lts/*]
        pnpm-version: [latest]
    runs-on: ${{ matrix.os }}
    steps:
      - name: ⬇️ Checkout
        id: checkout
        uses: actions/checkout@v2.3.3
        with:
          token: ${{ env.GITHUB_TOKEN }}
          fetch-depth: 0

      - name: 🟢 Setup node
        id: setup-node
        uses: actions/setup-node@v2
        with:
          node-version: ${{ matrix.node-version }}

      - name: 🥡 Setup pnpm
        id: setup-pnpm
        uses: pnpm/action-setup@v2.1.0
        with:
          version: ${{ matrix.pnpm-version }}
          run_install: false

      - name: 🎈 Get pnpm store directory
        id: get-pnpm-cache-dir
        run: |
          echo "::set-output name=pnpm_cache_dir::$(pnpm store path)"

      - name: 🔆 Cache pnpm modules
        uses: actions/cache@v3
        id: pnpm-cache
        with:
          path: ${{ steps.get-pnpm-cache-dir.outputs.pnpm_cache_dir }}
          key: ${{ runner.os }}-pnpm-store-${{ hashFiles('**/pnpm-lock.yaml') }}
          restore-keys: |
            ${{ runner.os }}-pnpm-store-

      - name: 🧩 Install Dependencies
        id: install-dependencies
        run: pnpm install

      - name: 🏗️ Build
        id: build-the-mono-repo
        run: pnpm build

      - name: 📣 Create Release Pull Request or Publish to npm
        id: changesets
        uses: changesets/action@v1
        with:
          title: "chore(release): version packages 🦋"
          publish: pnpm publish:packages
          version: pnpm version:packages
          commit: "chore(release): version packages 🦋 [skip ci]"
        env:
          GITHUB_TOKEN: ${{ env.GITHUB_TOKEN }}
          NPM_TOKEN: ${{ env.NPM_TOKEN }}
