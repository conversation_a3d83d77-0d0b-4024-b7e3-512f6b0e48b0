@tailwind base;
@tailwind components;
@tailwind utilities;

/* Import Neo-Brutalism Design System */
@import './neo-brutalism.css';

/* Import Drag Handle Position Fix */
@import './drag-handle-fix.css';

@layer base {
  html {
    font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", <PERSON><PERSON>, "Noto Sans", "PingFang SC", "Microsoft YaHei", "微软雅黑", sans-serif, "Apple Color Emoji", "Segoe UI Emoji", "Segoe UI Symbol", "Noto Color Emoji";
  }
  
  body {
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
  }
  :root {
    --background: 0 0% 100%;
    --foreground: 222.2 84% 4.9%;

    --card: 0 0% 100%;
    --card-foreground: 222.2 84% 4.9%;

    --popover: 0 0% 100%;
    --popover-foreground: 222.2 84% 4.9%;

    --primary: 222.2 47.4% 11.2%;
    --primary-foreground: 210 40% 98%;

    --secondary: 210 40% 96.1%;
    --secondary-foreground: 222.2 47.4% 11.2%;

    --muted: 210 40% 96.1%;
    --muted-foreground: 215.4 16.3% 46.9%;

    --accent: 210 40% 96.1%;
    --accent-foreground: 222.2 47.4% 11.2%;

    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 210 40% 98%;

    --border: 214.3 31.8% 91.4%;
    --input: 214.3 31.8% 91.4%;
    --ring: 222.2 84% 4.9%;

    --radius: 0.5rem;

    --novel-highlight-default: #ffffff;
    --novel-highlight-purple: #f6f3f8;
    --novel-highlight-red: #fdebeb;
    --novel-highlight-yellow: #fbf4a2;
    --novel-highlight-blue: #c1ecf9;
    --novel-highlight-green: #acf79f;
    --novel-highlight-orange: #faebdd;
    --novel-highlight-pink: #faf1f5;
    --novel-highlight-gray: #f1f1ef;
  }

  .dark {
    --background: 222.2 84% 4.9%;
    --foreground: 210 40% 98%;

    --card: 222.2 84% 4.9%;
    --card-foreground: 210 40% 98%;

    --popover: 222.2 84% 4.9%;
    --popover-foreground: 210 40% 98%;

    --primary: 210 40% 98%;
    --primary-foreground: 222.2 47.4% 11.2%;

    --secondary: 217.2 32.6% 17.5%;
    --secondary-foreground: 210 40% 98%;

    --muted: 217.2 32.6% 17.5%;
    --muted-foreground: 215 20.2% 65.1%;

    --accent: 217.2 32.6% 17.5%;
    --accent-foreground: 210 40% 98%;

    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 210 40% 98%;

    --border: 217.2 32.6% 17.5%;
    --input: 217.2 32.6% 17.5%;
    --ring: 212.7 26.8% 83.9%;

    --novel-highlight-default: #000000;
    --novel-highlight-purple: #3f2c4b;
    --novel-highlight-red: #5c1a1a;
    --novel-highlight-yellow: #5c4b1a;
    --novel-highlight-blue: #1a3d5c;
    --novel-highlight-green: #1a5c20;
    --novel-highlight-orange: #5c3a1a;
    --novel-highlight-pink: #5c1a3a;
    --novel-highlight-gray: #3a3a3a;
  }
}

@layer base {
  * {
    @apply border-border;
  }
  body {
    @apply bg-background text-foreground;
  }
}

/* A4 Page Layout Styles */
@layer components {
  .editor-a4-layout {
    /* A4 paper dimensions in pixels (at 96 DPI) */
    max-width: 794px; /* 210mm */
    min-height: 1123px; /* 297mm */
    width: 100%;
    margin: 0 auto;
    padding: 24px 72px 96px; /* Further reduced top padding for tighter layout */
    background: white;
    /* Neo-Brutalism styling */
    border-radius: 0;
    border: 4px solid black;
    box-shadow: 8px 8px 0px rgba(0, 0, 0, 1);
    overflow-wrap: break-word;
    word-wrap: break-word;
    hyphens: auto;
    transition: box-shadow 0.15s ease, transform 0.15s ease;
  }
  
  /* Add Neo-Brutalism hover effect */
  .editor-a4-layout:hover {
    box-shadow: 10px 10px 0px rgba(0, 0, 0, 1);
    transform: translate(-2px, -2px);
  }

  /* Desktop: maintain A4 proportions */
  @media (min-width: 850px) {
    .editor-a4-layout {
      width: 794px;
    }
  }

  /* Mobile: adjust padding and width */
  @media (max-width: 849px) {
    .editor-a4-layout {
      width: calc(100% - 32px);
      margin: 0 16px;
      padding: 12px 24px 48px; /* Further reduced top padding for mobile */
      min-height: auto;
    }
  }

  .editor-a4-layout.dark {
    background: #1f2937;
    color: white;
    border: 4px solid white;
    box-shadow: 8px 8px 0px rgba(255, 255, 255, 1);
  }
  
  .editor-a4-layout.dark:hover {
    box-shadow: 10px 10px 0px rgba(255, 255, 255, 1);
    transform: translate(-2px, -2px);
  }

  /* Ensure content within A4 layout doesn't overflow */
  .editor-a4-layout * {
    max-width: 100%;
    overflow-wrap: break-word;
    word-wrap: break-word;
  }

  /* Handle images and media within A4 layout */
  .editor-a4-layout img,
  .editor-a4-layout video,
  .editor-a4-layout iframe {
    max-width: 100%;
    height: auto;
  }

  /* Handle code blocks */
  .editor-a4-layout pre {
    overflow-x: auto;
    white-space: pre-wrap;
    word-wrap: break-word;
  }

  /* Handle tables */
  .editor-a4-layout table {
    width: 100%;
    table-layout: fixed;
    border-collapse: collapse;
    margin: 1em 0;
    border: 1px solid #e2e8f0;
  }

  .editor-a4-layout td,
  .editor-a4-layout th {
    word-wrap: break-word;
    overflow-wrap: break-word;
    border: 1px solid #e2e8f0;
    padding: 0.125em 1em;
    text-align: left;
    line-height: 1.2;
  }

  .editor-a4-layout th {
    background-color: #f7fafc;
    font-weight: 600;
  }

  /* Additional styles to reduce table cell spacing */
  .editor-a4-layout table tr {
    height: auto;
  }

  .editor-a4-layout table p {
    margin: 0;
    padding: 0;
    line-height: 1.2;
  }
}

/* Background styles for improved visibility */
body {
  transition: background-image 0.3s ease;
  position: relative;
}

#background-overlay {
  transition: opacity 0.3s ease;
}

/* Ensure content remains readable with background */
.bg-gradient-to-br {
  position: relative;
  z-index: 1;
}

/* Main content containers should be above background */
body > div:first-child {
  position: relative;
  z-index: 1;
}

/* Make sure sidebar and other UI elements stay on top */
.fixed {
  z-index: 100;
}

/* Enhanced backdrop blur for sidebar transparency */
.backdrop-blur-md {
  backdrop-filter: blur(12px);
  -webkit-backdrop-filter: blur(12px);
}

/* Custom slider styles */
.slider {
  background: linear-gradient(to right, #e5e7eb, #6b7280);
}

.slider::-webkit-slider-thumb {
  appearance: none;
  height: 16px;
  width: 16px;
  border-radius: 50%;
  background: #3b82f6;
  cursor: pointer;
  border: 2px solid #ffffff;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  transition: all 0.2s ease;
}

.slider::-webkit-slider-thumb:hover {
  background: #2563eb;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
  transform: scale(1.1);
}

.slider::-moz-range-thumb {
  height: 16px;
  width: 16px;
  border-radius: 50%;
  background: #3b82f6;
  cursor: pointer;
  border: 2px solid #ffffff;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  transition: all 0.2s ease;
}

.slider::-moz-range-thumb:hover {
  background: #2563eb;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
  transform: scale(1.1);
}

pre {
  background: #0d0d0d;
  border-radius: 0.5rem;
  color: #fff;
  font-family: "JetBrainsMono", monospace;
  padding: 0.75rem 1rem;

  code {
    background: none;
    color: inherit;
    font-size: 0.8rem;
    padding: 0;
  }

  .hljs-comment,
  .hljs-quote {
    color: #616161;
  }

  .hljs-variable,
  .hljs-template-variable,
  .hljs-attribute,
  .hljs-tag,
  .hljs-name,
  .hljs-regexp,
  .hljs-link,
  .hljs-name,
  .hljs-selector-id,
  .hljs-selector-class {
    color: #f98181;
  }

  .hljs-number,
  .hljs-meta,
  .hljs-built_in,
  .hljs-builtin-name,
  .hljs-literal,
  .hljs-type,
  .hljs-params {
    color: #fbbc88;
  }

  .hljs-string,
  .hljs-symbol,
  .hljs-bullet {
    color: #b9f18d;
  }

  .hljs-title,
  .hljs-section {
    color: #faf594;
  }

  .hljs-keyword,
  .hljs-selector-tag {
    color: #70cff8;
  }

  .hljs-emphasis {
    font-style: italic;
  }

  .hljs-strong {
    font-weight: 700;
  }
}
