/* 专门修复拖拽手柄定位的补丁文件 */
/* 这个文件确保拖拽手柄位置正确，不管第三方库如何设置 */

/* 最高优先级的拖拽手柄修复 - 使用更具体的选择器确保优先级 */
.ProseMirror .drag-handle,
.ProseMirror [data-drag-handle],
.ProseMirror .neo-drag-handle,
.drag-handle.hide,
.ProseMirror .drag-handle.hide,
.ProseMirror [class*="drag-handle"],
.ProseMirror [class*="drag"],
.prosemirror-dropcursor-block,
.pm-drag-handle,
/* 添加更多可能的选择器 */
  div[data-drag-handle],
span[data-drag-handle],
button[data-drag-handle] {
  /* 强制显示被隐藏的拖拽手柄 */
  display: block !important;
  visibility: visible !important;
  opacity: 1 !important;

  /* 精确定位 - 向上移动约1行的距离，减少偏移 */
  transform: translateY(-1rem) !important;
  /* 确保定位基准点正确 */
  position: absolute !important;

  /* Neo-Brutalism 样式 */
  border: 2px solid #000 !important;
  background-color: #fff !important;

  box-shadow: 3px 3px 0px rgba(0, 0, 0, 1) !important;
  border-radius: 0 !important;
  width: 1.5rem !important;
  height: 1.5rem !important;
  z-index: 999 !important;
  opacity: 1 !important;
  cursor: grab !important;
  transition: all 0.15s ease !important;

  /* 图标样式 */
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 10 10' style='fill: rgba(0, 0, 0, 0.8)'%3E%3Cpath d='M3,2 C2.44771525,2 2,1.55228475 2,1 C2,0.44771525 2.44771525,0 3,0 C3.55228475,0 4,0.44771525 4,1 C4,1.55228475 3.55228475,2 3,2 Z M3,6 C2.44771525,6 2,5.55228475 2,5 C2,4.44771525 2.44771525,4 3,4 C3.55228475,4 4,4.44771525 4,5 C4,5.55228475 3.55228475,6 3,6 Z M3,10 C2.44771525,10 2,9.55228475 2,9 C2,8.44771525 2.44771525,8 3,8 C3.55228475,8 4,8.44771525 4,9 C4,9.55228475 3.55228475,10 3,10 Z M7,2 C6.44771525,2 6,1.55228475 6,1 C6,0.44771525 6.44771525,0 7,0 C7.55228475,0 8,0.44771525 8,1 C8,1.55228475 7.55228475,2 7,2 Z M7,6 C6.44771525,6 6,5.55228475 6,5 C6,4.44771525 6.44771525,4 7,4 C7.55228475,4 8,4.44771525 8,5 C8,5.55228475 7.55228475,6 7,6 Z M7,10 C6.44771525,10 6,9.55228475 6,9 C6,8.44771525 6.44771525,8 7,8 C7.55228475,8 8,8.44771525 8,9 C8,9.55228475 7.55228475,10 7,10 Z'%3E%3C/path%3E%3C/svg%3E") !important;
  background-size: 14px 14px !important;
  background-repeat: no-repeat !important;
  background-position: center !important;
}

/* 悬停效果 */
.ProseMirror .drag-handle:hover,
.ProseMirror [data-drag-handle]:hover,
.ProseMirror .neo-drag-handle:hover,
div[data-drag-handle]:hover,
span[data-drag-handle]:hover,
button[data-drag-handle]:hover {
  background-color: #ffff00 !important;
  box-shadow: 4px 4px 0px rgba(0, 0, 0, 1) !important;
  transform: translateY(-1rem) translate(-1px, -1px) !important;
}

/* 点击效果 */
.ProseMirror .drag-handle:active,
.ProseMirror [data-drag-handle]:active,
.ProseMirror .neo-drag-handle:active,
div[data-drag-handle]:active,
span[data-drag-handle]:active,
button[data-drag-handle]:active {
  background-color: #ff3333 !important;
  box-shadow: 2px 2px 0px rgba(0, 0, 0, 1) !important;
  transform: translateY(-1rem) translate(1px, 1px) !important;
  cursor: grabbing !important;
}

/* 深色模式 */
.dark .ProseMirror .drag-handle,
.dark .ProseMirror [data-drag-handle],
.dark .ProseMirror .neo-drag-handle,
.dark div[data-drag-handle],
.dark span[data-drag-handle],
.dark button[data-drag-handle] {
  background-color: #1f2937 !important;
  border: 2px solid #fff !important;
  box-shadow: 3px 3px 0px rgba(255, 255, 255, 1) !important;
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 10 10' style='fill: rgba(255, 255, 255, 0.9)'%3E%3Cpath d='M3,2 C2.44771525,2 2,1.55228475 2,1 C2,0.44771525 2.44771525,0 3,0 C3.55228475,0 4,0.44771525 4,1 C4,1.55228475 3.55228475,2 3,2 Z M3,6 C2.44771525,6 2,5.55228475 2,5 C2,4.44771525 2.44771525,4 3,4 C3.55228475,4 4,4.44771525 4,5 C4,5.55228475 3.55228475,6 3,6 Z M3,10 C2.44771525,10 2,9.55228475 2,9 C2,8.44771525 2.44771525,8 3,8 C3.55228475,8 4,8.44771525 4,9 C4,9.55228475 3.55228475,10 3,10 Z M7,2 C6.44771525,2 6,1.55228475 6,1 C6,0.44771525 6.44771525,0 7,0 C7.55228475,0 8,0.44771525 8,1 C8,1.55228475 7.55228475,2 7,2 Z M7,6 C6.44771525,6 6,5.55228475 6,5 C6,4.44771525 6.44771525,4 7,4 C7.55228475,4 8,4.44771525 8,5 C8,5.55228475 7.55228475,6 7,6 Z M7,10 C6.44771525,10 6,9.55228475 6,9 C6,8.44771525 6.44771525,8 7,8 C7.55228475,8 8,8.44771525 8,9 C8,9.55228475 7.55228475,10 7,10 Z'%3E%3C/path%3E%3C/svg%3E") !important;
}

.dark .ProseMirror .drag-handle:hover,
.dark .ProseMirror [data-drag-handle]:hover,
.dark .ProseMirror .neo-drag-handle:hover,
.dark div[data-drag-handle]:hover,
.dark span[data-drag-handle]:hover,
.dark button[data-drag-handle]:hover {
  background-color: #ffff00 !important;
  border: 2px solid #000 !important;
  box-shadow: 4px 4px 0px rgba(0, 0, 0, 1) !important;
  transform: translateY(-1rem) translate(-1px, -1px) !important;
}

.dark .ProseMirror .drag-handle:active,
.dark .ProseMirror [data-drag-handle]:active,
.dark .ProseMirror .neo-drag-handle:active,
.dark div[data-drag-handle]:active,
.dark span[data-drag-handle]:active,
.dark button[data-drag-handle]:active {
  background-color: #ff3333 !important;
  border: 2px solid #000 !important;
  box-shadow: 2px 2px 0px rgba(0, 0, 0, 1) !important;
  transform: translateY(-1rem) translate(1px, 1px) !important;
}

/* 移除备用定位规则 - position: fixed 不需要这些复杂的回退逻辑 */

/* 响应式处理 */
@media screen and (max-width: 600px) {
  .ProseMirror .drag-handle,
  .ProseMirror [data-drag-handle],
  .ProseMirror .neo-drag-handle,
  div[data-drag-handle],
  span[data-drag-handle],
  button[data-drag-handle] {
    display: none !important;
    pointer-events: none !important;
  }
}

/* 最终覆盖规则 - 确保定位修复生效 */
/* 使用更高的特异性来覆盖任何可能的冲突样式 */
.ProseMirror .ProseMirror .drag-handle,
.ProseMirror .ProseMirror [data-drag-handle],
.ProseMirror .ProseMirror .neo-drag-handle,
.ProseMirror div[data-drag-handle],
.ProseMirror span[data-drag-handle],
.ProseMirror button[data-drag-handle] {
  transform: translateY(-1rem) !important;
  position: absolute !important;
}

/* 悬停和激活状态的最终覆盖 */
.ProseMirror .ProseMirror .drag-handle:hover,
.ProseMirror .ProseMirror [data-drag-handle]:hover,
.ProseMirror .ProseMirror .neo-drag-handle:hover,
.ProseMirror div[data-drag-handle]:hover,
.ProseMirror span[data-drag-handle]:hover,
.ProseMirror button[data-drag-handle]:hover {
  transform: translateY(-1rem) translate(-1px, -1px) !important;
}

.ProseMirror .ProseMirror .drag-handle:active,
.ProseMirror .ProseMirror [data-drag-handle]:active,
.ProseMirror .ProseMirror .neo-drag-handle:active,
.ProseMirror div[data-drag-handle]:active,
.ProseMirror span[data-drag-handle]:active,
.ProseMirror button[data-drag-handle]:active {
  transform: translateY(-1rem) translate(1px, 1px) !important;
}
