.ProseMirror {
  @apply p-12 px-8 sm:px-12;
}

.ProseMirror .is-editor-empty:first-child::before {
  content: attr(data-placeholder);
  float: left;
  color: hsl(var(--muted-foreground));
  pointer-events: none;
  height: 0;
}
.ProseMirror .is-empty::before {
  content: attr(data-placeholder);
  float: left;
  color: hsl(var(--muted-foreground));
  pointer-events: none;
  height: 0;
}

/* Ghost text for AI autocomplete */
.ProseMirror .ghost-text {
  color: #9ca3af !important;
  font-style: normal !important;
  font-weight: normal !important;
  pointer-events: none !important;
  user-select: none !important;
  opacity: 0.5 !important;
  position: relative !important;
  display: inline !important;
  white-space: pre-wrap !important;
  word-wrap: break-word !important;
  overflow-wrap: break-word !important;
  font-size: inherit !important;
  line-height: inherit !important;
  max-width: 100% !important;
  box-sizing: border-box !important;
}

.ProseMirror .ghost-text-container {
  color: #9ca3af !important;
  font-style: normal !important;
  font-weight: normal !important;
  pointer-events: none !important;
  user-select: none !important;
  opacity: 0.5 !important;
  display: inline-block !important;
  max-width: 100% !important;
  font-size: inherit !important;
  line-height: inherit !important;
  vertical-align: top !important;
  box-sizing: border-box !important;
}

.ProseMirror .ghost-text-line {
  white-space: pre-wrap !important;
  word-wrap: break-word !important;
  overflow-wrap: break-word !important;
  max-width: 100% !important;
  box-sizing: border-box !important;
}

/* Custom image styles */

.ProseMirror img {
  transition: filter 0.1s ease-in-out;

  &:hover {
    cursor: pointer;
    filter: brightness(90%);
  }

  &.ProseMirror-selectednode {
    outline: 3px solid #5abbf7;
    filter: brightness(90%);
  }
}

.img-placeholder {
  position: relative;

  &:before {
    content: "";
    box-sizing: border-box;
    position: absolute;
    top: 50%;
    left: 50%;
    width: 36px;
    height: 36px;
    border-radius: 50%;
    border: 3px solid var(--novel-stone-200);
    border-top-color: var(--novel-stone-800);
    animation: spinning 0.6s linear infinite;
  }
}

@keyframes spinning {
  to {
    transform: rotate(360deg);
  }
}

/* Custom TODO list checkboxes – shoutout to this awesome tutorial: https://moderncss.dev/pure-css-custom-checkbox-style/ */

ul[data-type="taskList"] li > label {
  margin-right: 0.2rem;
  user-select: none;
}

@media screen and (max-width: 768px) {
  ul[data-type="taskList"] li > label {
    margin-right: 0.5rem;
  }
}

ul[data-type="taskList"] li > label input[type="checkbox"] {
  -webkit-appearance: none;
  appearance: none;
  background-color: hsl(var(--background));
  margin: 0;
  cursor: pointer;
  width: 1.2em;
  height: 1.2em;
  position: relative;
  top: 5px;
  border: 2px solid hsl(var(--border));
  margin-right: 0.3rem;
  display: grid;
  place-content: center;

  &:hover {
    background-color: hsl(var(--accent));
  }

  &:active {
    background-color: hsl(var(--accent));
  }

  &::before {
    content: "";
    width: 0.65em;
    height: 0.65em;
    transform: scale(0);
    transition: 120ms transform ease-in-out;
    box-shadow: inset 1em 1em;
    transform-origin: center;
    clip-path: polygon(14% 44%, 0 65%, 50% 100%, 100% 16%, 80% 0%, 43% 62%);
  }

  &:checked::before {
    transform: scale(1);
  }
}

ul[data-type="taskList"] li[data-checked="true"] > div > p {
  color: var(--muted-foreground);
  text-decoration: line-through;
  text-decoration-thickness: 2px;
}

/* Overwrite tippy-box original max-width */

.tippy-box {
  max-width: 400px !important;
}

.ProseMirror:not(.dragging) .ProseMirror-selectednode {
  outline: none !important;
  background-color: var(--novel-highlight-blue);
  transition: background-color 0.2s;
  box-shadow: none;
}

.drag-handle {
  opacity: 1;
  transition: all 0.15s ease;
  border-radius: 0;
  border: 2px solid #000;
  background-color: #fff;
  box-shadow: 3px 3px 0px rgba(0, 0, 0, 1);

  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 10 10' style='fill: rgba(0, 0, 0, 0.8)'%3E%3Cpath d='M3,2 C2.44771525,2 2,1.55228475 2,1 C2,0.44771525 2.44771525,0 3,0 C3.55228475,0 4,0.44771525 4,1 C4,1.55228475 3.55228475,2 3,2 Z M3,6 C2.44771525,6 2,5.55228475 2,5 C2,4.44771525 2.44771525,4 3,4 C3.55228475,4 4,4.44771525 4,5 C4,5.55228475 3.55228475,6 3,6 Z M3,10 C2.44771525,10 2,9.55228475 2,9 C2,8.44771525 2.44771525,8 3,8 C3.55228475,8 4,8.44771525 4,9 C4,9.55228475 3.55228475,10 3,10 Z M7,2 C6.44771525,2 6,1.55228475 6,1 C6,0.44771525 6.44771525,0 7,0 C7.55228475,0 8,0.44771525 8,1 C8,1.55228475 7.55228475,2 7,2 Z M7,6 C6.44771525,6 6,5.55228475 6,5 C6,4.44771525 6.44771525,4 7,4 C7.55228475,4 8,4.44771525 8,5 C8,5.55228475 7.55228475,6 7,6 Z M7,10 C6.44771525,10 6,9.55228475 6,9 C6,8.44771525 6.44771525,8 7,8 C7.55228475,8 8,8.44771525 8,9 C8,9.55228475 7.55228475,10 7,10 Z'%3E%3C/path%3E%3C/svg%3E");
  background-size: calc(0.5em + 0.375rem) calc(0.5em + 0.375rem);
  background-repeat: no-repeat;
  background-position: center;
  width: 1.2rem;
  height: 1.5rem;
  z-index: 50;
  cursor: grab;

  &:hover {
    background-color: #ffff00;
    box-shadow: 4px 4px 0px rgba(0, 0, 0, 1);
    transform: translate(-1px, -1px);
    transition: all 0.15s ease;
  }

  &:active {
    background-color: #ff3333;
    box-shadow: 2px 2px 0px rgba(0, 0, 0, 1);
    transform: translate(1px, 1px);
    cursor: grabbing;
    transition: all 0.15s ease;
  }

  &.hide {
    opacity: 0;
    pointer-events: none;
  }

  @media screen and (max-width: 600px) {
    display: none;
    pointer-events: none;
  }
}

.dark .drag-handle {
  background-color: #1f2937;
  border: 2px solid #fff;
  box-shadow: 3px 3px 0px rgba(255, 255, 255, 1);
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 10 10' style='fill: rgba(255, 255, 255, 0.9)'%3E%3Cpath d='M3,2 C2.44771525,2 2,1.55228475 2,1 C2,0.44771525 2.44771525,0 3,0 C3.55228475,0 4,0.44771525 4,1 C4,1.55228475 3.55228475,2 3,2 Z M3,6 C2.44771525,6 2,5.55228475 2,5 C2,4.44771525 2.44771525,4 3,4 C3.55228475,4 4,4.44771525 4,5 C4,5.55228475 3.55228475,6 3,6 Z M3,10 C2.44771525,10 2,9.55228475 2,9 C2,8.44771525 2.44771525,8 3,8 C3.55228475,8 4,8.44771525 4,9 C4,9.55228475 3.55228475,10 3,10 Z M7,2 C6.44771525,2 6,1.55228475 6,1 C6,0.44771525 6.44771525,0 7,0 C7.55228475,0 8,0.44771525 8,1 C8,1.55228475 7.55228475,2 7,2 Z M7,6 C6.44771525,6 6,5.55228475 6,5 C6,4.44771525 6.44771525,4 7,4 C7.55228475,4 8,4.44771525 8,5 C8,5.55228475 7.55228475,6 7,6 Z M7,10 C6.44771525,10 6,9.55228475 6,9 C6,8.44771525 6.44771525,8 7,8 C7.55228475,8 8,8.44771525 8,9 C8,9.55228475 7.55228475,10 7,10 Z'%3E%3C/path%3E%3C/svg%3E");

  &:hover {
    background-color: #ffff00;
    border: 2px solid #000;
    box-shadow: 4px 4px 0px rgba(0, 0, 0, 1);
    transform: translate(-1px, -1px);
  }

  &:active {
    background-color: #ff3333;
    border: 2px solid #000;
    box-shadow: 2px 2px 0px rgba(0, 0, 0, 1);
    transform: translate(1px, 1px);
  }
}

/* Custom Youtube Video CSS */
iframe {
  border: 8px solid #ffd00027;
  border-radius: 4px;
  min-width: 200px;
  min-height: 200px;
  display: block;
  outline: 0px solid transparent;
}

div[data-youtube-video] > iframe {
  cursor: move;
  aspect-ratio: 16 / 9;
  width: 100%;
}

.ProseMirror-selectednode iframe {
  transition: outline 0.15s;
  outline: 6px solid #fbbf24;
}

@media only screen and (max-width: 480px) {
  div[data-youtube-video] > iframe {
    max-height: 50px;
  }
}

@media only screen and (max-width: 720px) {
  div[data-youtube-video] > iframe {
    max-height: 100px;
  }
}

/* CSS for bold coloring and highlighting issue*/
span[style] > strong {
  color: inherit;
}

mark[style] > strong {
  color: inherit;
}

/* Fix for bubble menu icons */
.ProseMirror .lucide {
  display: inline-block;
  fill: none;
  stroke: currentColor;
  stroke-linecap: round;
  stroke-linejoin: round;
  stroke-width: 2;
}

/* Ensure SVG icons are properly sized and displayed */
.ProseMirror svg {
  display: inline-block;
  vertical-align: middle;
}

/* Fix for text buttons in bubble menu - Enhanced */
[data-bubble-menu] svg,
.tippy-content svg,
.editor-bubble svg,
.tippy-box svg,
div[data-tippy-root] svg {
  display: inline-block !important;
  fill: none !important;
  stroke: currentColor !important;
  stroke-linecap: round !important;
  stroke-linejoin: round !important;
  stroke-width: 2 !important;
  width: 1rem !important;
  height: 1rem !important;
  visibility: visible !important;
  opacity: 1 !important;
}

/* Specific bubble menu button SVG fixes */
.tippy-content button svg,
[data-bubble-menu] button svg {
  display: inline-block !important;
  fill: none !important;
  stroke: currentColor !important;
  stroke-linecap: round !important;
  stroke-linejoin: round !important;
  stroke-width: 2 !important;
  width: 16px !important;
  height: 16px !important;
  visibility: visible !important;
  opacity: 1 !important;
  pointer-events: none !important;
}

/* General lucide icon fix */
.lucide {
  display: inline-block !important;
  fill: none !important;
  stroke: currentColor !important;
  stroke-linecap: round !important;
  stroke-linejoin: round !important;
  stroke-width: 2 !important;
  visibility: visible !important;
  opacity: 1 !important;
}

/* Table styles for ProseMirror */
.ProseMirror table {
  border-collapse: collapse;
  table-layout: fixed;
  width: 100%;
  margin: 1em 0;
  overflow: hidden;
  border: 1px solid #e2e8f0;
  border-spacing: 0;
}

.ProseMirror td,
.ProseMirror th {
  min-width: 1em;
  border: 1px solid #e2e8f0;
  padding: 0.125em 1em;
  vertical-align: top;
  box-sizing: border-box;
  position: relative;
  word-wrap: break-word;
  overflow-wrap: break-word;
  line-height: 1.2;
}

.ProseMirror th {
  font-weight: 600;
  text-align: left;
  background-color: #f7fafc;
}

.ProseMirror .selectedCell:after {
  z-index: 2;
  position: absolute;
  content: "";
  left: 0; right: 0; top: 0; bottom: 0;
  background: rgba(200, 200, 255, 0.4);
  pointer-events: none;
}

/* Additional styles to reduce table cell spacing */
.ProseMirror table tr {
  height: auto;
}

.ProseMirror table p {
  margin: 0;
  padding: 0;
  line-height: 1.2;
}

/* 禁用表格默认拖拽行为 */
.ProseMirror table {
  -webkit-user-drag: none;
  -khtml-user-drag: none;
  -moz-user-drag: none;
  -o-user-drag: none;
  user-drag: none;
}

.ProseMirror td, .ProseMirror th {
  -webkit-user-drag: none;
  -khtml-user-drag: none;
  -moz-user-drag: none;
  -o-user-drag: none;
  user-drag: none;
}

/* 隐藏表格内的全局拖拽手柄 */
.ProseMirror table .drag-handle {
  display: none !important;
  pointer-events: none !important;
}

/* 确保表格行不显示拖拽手柄 */
.ProseMirror tr .drag-handle {
  display: none !important;
}

.ProseMirror .column-resize-handle {
  position: absolute;
  right: -2px;
  top: 0;
  bottom: -2px;
  width: 4px;
  background-color: #adf;
  pointer-events: none;
}

.ProseMirror table .tableWrapper {
  padding: 1rem 0;
  overflow-x: auto;
}

.ProseMirror .resize-cursor {
  cursor: ew-resize;
  cursor: col-resize;
}

/* Button icon fix */
button svg {
  display: inline-block !important;
  vertical-align: middle !important;
  visibility: visible !important;
  opacity: 1 !important;
}

/* Force SVG path visibility */
svg path {
  visibility: visible !important;
  opacity: 1 !important;
}
