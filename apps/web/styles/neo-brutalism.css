/* Neo-Brutalism Design System for FuckNotion */
/* Inspired by Figma's bold, geometric design language */

/* Import Neo-Brutalism fonts */
@import url('https://fonts.googleapis.com/css2?family=Inter:wght@400;600;700;800;900&display=swap');
@import url('https://fonts.googleapis.com/css2?family=Montserrat:wght@400;600;700;800;900&display=swap');
@import url('https://fonts.googleapis.com/css2?family=Oswald:wght@400;500;600;700&display=swap');

:root {
  /* Neo-Brutalism Colors */
  --neo-black: #000000;
  --neo-white: #ffffff;
  --neo-red: #ff3333;
  --neo-blue: #0066ff;
  --neo-yellow: #ffff00;
  --neo-green: #00ff66;
  --neo-purple: #9933ff;
  --neo-orange: #ff6600;
  --neo-pink: #ff3399;
  --neo-cyan: #00ffff;
  
  /* Grays */
  --neo-gray-100: #f5f5f5;
  --neo-gray-200: #e5e5e5;
  --neo-gray-300: #d4d4d4;
  --neo-gray-800: #262626;
  --neo-gray-900: #171717;
  
  /* Shadows */
  --neo-shadow-sm: 2px 2px 0px var(--neo-black);
  --neo-shadow-md: 4px 4px 0px var(--neo-black);
  --neo-shadow-lg: 6px 6px 0px var(--neo-black);
  --neo-shadow-xl: 8px 8px 0px var(--neo-black);
  
  /* Borders */
  --neo-border: 3px solid var(--neo-black);
  --neo-border-thick: 4px solid var(--neo-black);
  
  /* Typography - Neo-Brutalism Font Stack */
  /* Primary: 正文和界面文字 */
  --neo-font-family-primary: 'Inter', 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', '微软雅黑', 'Source Han Sans SC', 'Noto Sans CJK SC', 'WenQuanYi Micro Hei', sans-serif;

  /* Display: 标题和强调文字 */
  --neo-font-family-display: 'Montserrat', 'Inter', 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', '微软雅黑', 'Source Han Sans SC', sans-serif;

  /* Condensed: 压缩字体，用于特殊标题 */
  --neo-font-family-condensed: 'Oswald', 'Inter', 'PingFang SC', 'Microsoft YaHei', '微软雅黑', sans-serif;

  /* Font Weights */
  --neo-font-weight-normal: 600;
  --neo-font-weight-bold: 700;
  --neo-font-weight-extra-bold: 800;
  --neo-font-weight-black: 900;
}

/* Base Neo-Brutalism Classes */
.neo-card {
  background: var(--neo-white);
  border: var(--neo-border);
  box-shadow: var(--neo-shadow-md);
  border-radius: 0;
  transition: all 0.1s ease;
}

.neo-card:hover {
  box-shadow: var(--neo-shadow-lg);
  transform: translate(-2px, -2px);
}

.neo-button {
  background: var(--neo-white);
  border: var(--neo-border);
  box-shadow: var(--neo-shadow-sm);
  border-radius: 0;
  font-weight: 700;
  font-family: var(--neo-font-family);
  text-transform: uppercase;
  letter-spacing: 0.5px;
  transition: all 0.1s ease;
  cursor: pointer;
}

.neo-button:hover {
  box-shadow: var(--neo-shadow-md);
  transform: translate(-1px, -1px);
}

.neo-button:active {
  box-shadow: var(--neo-shadow-sm);
  transform: translate(1px, 1px);
}

.neo-button-primary {
  background: var(--neo-blue);
  color: var(--neo-white);
}

.neo-button-danger {
  background: var(--neo-red);
  color: var(--neo-white);
}

.neo-button-success {
  background: var(--neo-green);
  color: var(--neo-black);
}

.neo-input {
  background: var(--neo-white);
  border: var(--neo-border);
  border-radius: 0;
  font-family: var(--neo-font-family);
  font-weight: 600;
  box-shadow: inset 2px 2px 0px var(--neo-gray-200);
}

.neo-input:focus {
  outline: none;
  box-shadow: inset 2px 2px 0px var(--neo-gray-300), 0 0 0 3px var(--neo-blue);
}

.neo-sidebar {
  background: var(--neo-gray-100);
  border-right: var(--neo-border-thick);
  box-shadow: var(--neo-shadow-lg);
}

.neo-page-item {
  background: var(--neo-white);
  border: var(--neo-border);
  box-shadow: var(--neo-shadow-sm);
  margin: 8px 0;
  transition: all 0.1s ease;
}

.neo-page-item:hover {
  box-shadow: var(--neo-shadow-md);
  transform: translate(-1px, -1px);
}

.neo-page-item.active {
  background: var(--neo-yellow);
  box-shadow: var(--neo-shadow-md);
}

/* Subpage item styles are defined later in the file */

/* Typography */
.neo-heading {
  font-family: var(--neo-font-family-display);
  font-weight: var(--neo-font-weight-black);
  text-transform: uppercase;
  letter-spacing: 1px;
  /* Color should be set by parent context */
}

.neo-heading-condensed {
  font-family: var(--neo-font-family-condensed);
  font-weight: var(--neo-font-weight-bold);
  text-transform: uppercase;
  letter-spacing: 0.5px;
  line-height: 1.0;
  /* Color should be set by parent context */
}

.neo-text {
  font-family: var(--neo-font-family-primary);
  font-weight: var(--neo-font-weight-normal);
  /* Color should be set by parent context */
}

.neo-text-bold {
  font-family: var(--neo-font-family-primary);
  font-weight: var(--neo-font-weight-bold);
  /* Color should be set by parent context */
}

.neo-text-black {
  font-family: var(--neo-font-family-primary);
  font-weight: var(--neo-font-weight-black);
  color: var(--neo-black);
}

/* Color variants for different backgrounds */
.neo-heading-white {
  color: var(--neo-white) !important;
}

.neo-heading-black {
  color: var(--neo-black) !important;
}

.neo-text-white {
  color: var(--neo-white) !important;
}

.neo-text-black-color {
  color: var(--neo-black) !important;
}

/* Layout */
.neo-container {
  background: var(--neo-white);
  min-height: 100vh;
}

.neo-header {
  background: var(--neo-black);
  color: var(--neo-white);
  border-bottom: var(--neo-border-thick);
  box-shadow: var(--neo-shadow-lg);
  position: relative;
  z-index: 10;
}

/* Editor specific styles */
.editor-a4-layout.neo-editor {
  background: var(--neo-white);
  border: var(--neo-border-thick);
  box-shadow: var(--neo-shadow-xl);
  border-radius: 0;
  margin: 20px;
  padding: 40px;
}

/* Fix button hover states */
.neo-button:hover:not(.neo-button-primary):not(.neo-button-danger):not(.neo-button-success) {
  background: var(--neo-gray-100);
}

/* Active page styles */
.neo-page-item.active {
  background: var(--neo-yellow);
  box-shadow: var(--neo-shadow-lg);
  transform: translate(-2px, -2px);
}

/* Parent page styles */
.neo-parent-page {
  position: relative;
  margin-left: 0 !important;
  padding-left: 8px;
}

.neo-parent-page::before {
  content: '';
  position: absolute;
  left: 0;
  top: 0;
  bottom: 0;
  width: 4px;
  background: var(--neo-blue);
  border-radius: 0;
}

/* Subpage connector line */
.neo-subpage-item {
  position: relative;
}

.neo-subpage-item::before {
  content: '';
  position: absolute;
  left: -16px;
  top: 50%;
  width: 12px;
  height: 3px;
  background: var(--neo-black);
  transform: translateY(-50%);
}

/* Command palette and dropdown styles */
.neo-command-item {
  background: var(--neo-white);
  border: 2px solid var(--neo-black);
  box-shadow: 2px 2px 0px var(--neo-black);
  margin: 2px 0;
  padding: 8px 12px;
  transition: all 0.1s ease;
  cursor: pointer;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.neo-command-item:hover {
  background: var(--neo-yellow);
  box-shadow: 3px 3px 0px var(--neo-black);
  transform: translate(-1px, -1px);
}

/* Slash command menu styles */
.neo-slash-command-menu {
  background: var(--neo-white);
  border: 4px solid var(--neo-black);
  box-shadow: 8px 8px 0px var(--neo-black);
  border-radius: 0;
  padding: 16px;
  z-index: 50;
  max-height: 330px;
  overflow-y: auto;
}

.neo-slash-command-item {
  background: #f9f9f9;
  border: 2px solid var(--neo-black);
  box-shadow: 3px 3px 0px var(--neo-black);
  margin-bottom: 8px;
  padding: 10px 12px;
  transition: all 0.15s ease;
  cursor: pointer;
}

.neo-slash-command-item:hover,
.neo-slash-command-item[aria-selected="true"] {
  background: var(--neo-yellow);
  box-shadow: 4px 4px 0px var(--neo-black);
  transform: translate(-1px, -1px);
}

.neo-command-item[aria-selected="true"] {
  background: var(--neo-blue);
  color: var(--neo-white);
  box-shadow: 3px 3px 0px var(--neo-black);
  transform: translate(-1px, -1px);
}

/* Loading spinner */
.neo-spinner {
  border: 4px solid var(--neo-gray-200);
  border-top: 4px solid var(--neo-black);
  border-radius: 0;
  width: 40px;
  height: 40px;
  animation: neo-spin 1s linear infinite;
}

@keyframes neo-spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* Form elements */
.neo-form-input {
  background: var(--neo-white);
  border: var(--neo-border);
  border-radius: 0;
  padding: 12px 16px;
  font-family: var(--neo-font-family);
  font-weight: 600;
  box-shadow: inset 2px 2px 0px var(--neo-gray-200);
  transition: all 0.1s ease;
}

.neo-form-input:focus {
  outline: none;
  box-shadow: inset 2px 2px 0px var(--neo-gray-300), 0 0 0 4px var(--neo-blue);
  border-color: var(--neo-blue);
}

/* Placeholder text */
.neo-form-input::placeholder {
  color: var(--neo-gray-800);
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

/* Sidebar specific enhancements */
.neo-sidebar {
  background: var(--neo-white);
  border-right: 6px solid var(--neo-black);
  box-shadow: 6px 0 0px var(--neo-black);
}

.neo-logo-container {
  transition: transform 0.2s ease;
}

.neo-logo-container:hover {
  transform: scale(1.1) rotate(-2deg);
}

/* Decorative geometric shapes */
.neo-geometric-accent {
  position: absolute;
  background: var(--neo-yellow);
  border: 2px solid var(--neo-black);
  transform: rotate(45deg);
  opacity: 0.8;
  transition: all 0.3s ease;
}

.neo-geometric-accent:hover {
  transform: rotate(45deg) scale(1.2);
  opacity: 1;
}

/* Enhanced button effects */
.neo-button:hover {
  transform: translate(-2px, -2px);
  box-shadow: 4px 4px 0px var(--neo-black);
}

.neo-button:active {
  transform: translate(0px, 0px);
  box-shadow: 2px 2px 0px var(--neo-black);
}

/* Pulse animation for decorative elements */
@keyframes neo-pulse {
  0%, 100% { opacity: 0.6; transform: scale(1); }
  50% { opacity: 1; transform: scale(1.1); }
}

.neo-pulse {
  animation: neo-pulse 2s infinite;
}

/* Glitch effect for special elements */
@keyframes neo-glitch {
  0%, 100% { transform: translate(0); }
  20% { transform: translate(-2px, 2px); }
  40% { transform: translate(-2px, -2px); }
  60% { transform: translate(2px, 2px); }
  80% { transform: translate(2px, -2px); }
}

.neo-glitch:hover {
  animation: neo-glitch 0.3s ease-in-out;
}

/* Animations */
@keyframes neo-bounce {
  0%, 20%, 50%, 80%, 100% {
    transform: translateY(0);
  }
  40% {
    transform: translateY(-4px);
  }
  60% {
    transform: translateY(-2px);
  }
}

.neo-bounce {
  animation: neo-bounce 0.6s ease;
}

/* Responsive */
@media (max-width: 768px) {
  .neo-card {
    box-shadow: var(--neo-shadow-sm);
  }
  
  .neo-button {
    font-size: 14px;
    padding: 8px 16px;
  }
}
