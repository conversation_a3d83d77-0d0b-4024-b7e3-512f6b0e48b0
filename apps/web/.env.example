# This file will be committed to version control, so make sure not to have any
# secrets in it. If you are cloning this repo, create a copy of this file named
# ".env" and populate it with your secrets.

# Get your OpenAI API key here: https://platform.openai.com/account/api-keys
# For OpenRouter: Use your OpenRouter API key (sk-or-...) here
OPENAI_API_KEY=
# OPTIONAL: OpenAI Base URL (default to https://api.openai.com/v1)
# For OpenRouter: Set this to https://openrouter.ai/api/v1
OPENAI_BASE_URL=
# OPTIONAL: Model name (default to gpt-4o-mini)
# For OpenRouter: Use models like anthropic/claude-3-opus, anthropic/claude-3-sonnet, etc.
OPENAI_MODEL=

# OPTIONAL: Vercel Blob (for uploading images)
# Get your Vercel Blob credentials here: https://vercel.com/docs/storage/vercel-blob/quickstart#quickstart
BLOB_READ_WRITE_TOKEN=

# OPTIONAL: Vercel KV (for ratelimiting)
# Get your Vercel KV credentials here: https://vercel.com/docs/storage/vercel-kv/quickstart#quickstart
KV_REST_API_URL=
KV_REST_API_TOKEN=
