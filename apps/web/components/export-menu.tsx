"use client";

import { useState } from "react";
import { Button } from "@/components/tailwind/ui/button";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/tailwind/ui/dropdown-menu";
import { Download, FileText, File } from "lucide-react";
import { exportToMarkdown, exportToPDF } from "@/lib/export-utils";

interface ExportMenuProps {
  title: string;
  content: any;
  pageSlug: string;
}

export default function ExportMenu({ title, content, pageSlug }: ExportMenuProps) {
  const [isExporting, setIsExporting] = useState(false);

  const handleExportMarkdown = async () => {
    try {
      setIsExporting(true);
      await exportToMarkdown(title, content, pageSlug);
    } catch (error) {
      console.error("导出Markdown失败:", error);
      alert("导出失败，请重试");
    } finally {
      setIsExporting(false);
    }
  };

  const handleExportPDF = async () => {
    try {
      setIsExporting(true);
      await exportToPDF(title, content, pageSlug);
    } catch (error) {
      console.error("导出PDF失败:", error);
      alert("导出失败，请重试");
    } finally {
      setIsExporting(false);
    }
  };

  return (
    <DropdownMenu>
      <DropdownMenuTrigger asChild>
        <Button variant="ghost" size="sm" className="neo-button gap-2 bg-white text-black border-4 border-black shadow-[3px_3px_0px_0px_rgba(0,0,0,1)] hover:bg-orange-400 hover:shadow-[4px_4px_0px_0px_rgba(0,0,0,1)] hover:translate-x-[-1px] hover:translate-y-[-1px] transition-all duration-150" disabled={isExporting}>
          <Download className="h-4 w-4" />
          <span className="neo-text-bold uppercase tracking-wide">{isExporting ? "EXPORTING..." : "EXPORT"}</span>
        </Button>
      </DropdownMenuTrigger>
      <DropdownMenuContent align="end" className="neo-card p-2 bg-white border-4 border-black shadow-[6px_6px_0px_0px_rgba(0,0,0,1)]">
        <DropdownMenuItem onClick={handleExportMarkdown} disabled={isExporting} className="neo-command-item mb-2 last:mb-0 bg-gray-50 border-2 border-black shadow-[2px_2px_0px_0px_rgba(0,0,0,1)] hover:bg-yellow-400 hover:shadow-[3px_3px_0px_0px_rgba(0,0,0,1)] hover:translate-x-[-1px] hover:translate-y-[-1px] transition-all duration-150">
          <FileText className="h-4 w-4 mr-2" />
          <span className="neo-text-bold uppercase tracking-wide text-black">MARKDOWN</span>
        </DropdownMenuItem>
        <DropdownMenuItem onClick={handleExportPDF} disabled={isExporting} className="neo-command-item mb-2 last:mb-0 bg-gray-50 border-2 border-black shadow-[2px_2px_0px_0px_rgba(0,0,0,1)] hover:bg-yellow-400 hover:shadow-[3px_3px_0px_0px_rgba(0,0,0,1)] hover:translate-x-[-1px] hover:translate-y-[-1px] transition-all duration-150">
          <File className="h-4 w-4 mr-2" />
          <span className="neo-text-bold uppercase tracking-wide text-black">PDF</span>
        </DropdownMenuItem>
      </DropdownMenuContent>
    </DropdownMenu>
  );
}
