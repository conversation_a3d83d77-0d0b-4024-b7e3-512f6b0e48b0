{"name": "novel-next-app", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev -H 0.0.0.0", "build": "next build", "build:desktop": "cross-env BUILD_MODE=desktop NODE_ENV=production next build", "export": "cross-env BUILD_MODE=desktop NODE_ENV=production next export -o out", "start": "next start", "lint": "biome lint .", "format": "biome format . ", "typecheck": "tsc --noEmit"}, "dependencies": {"@ai-sdk/openai": "^1.1.0", "@radix-ui/react-dialog": "^1.0.5", "@radix-ui/react-dropdown-menu": "^2.1.15", "@radix-ui/react-label": "^2.0.2", "@radix-ui/react-popover": "^1.0.7", "@radix-ui/react-scroll-area": "^1.0.5", "@radix-ui/react-select": "^2.0.0", "@radix-ui/react-separator": "^1.0.3", "@radix-ui/react-slot": "^1.0.2", "@tailwindcss/typography": "^0.5.10", "@tauri-apps/api": "^2.6.0", "@tiptap/core": "^2.25.0", "@tiptap/pm": "^2.25.0", "@tiptap/react": "^2.25.0", "@upstash/ratelimit": "^1.0.1", "@vercel/analytics": "^1.2.2", "@vercel/blob": "^0.22.1", "@vercel/kv": "^1.0.1", "ai": "^3.0.12", "autoprefixer": "^10.4.17", "class-variance-authority": "^0.7.0", "clsx": "^2.1.0", "cmdk": "^1.0.4", "dexie": "^4.0.1", "eventsource-parser": "^1.1.2", "highlight.js": "^11.9.0", "lowlight": "^3.1.0", "lucide-react": "^0.358.0", "next": "15.1.4", "next-themes": "^0.2.1", "novel": "workspace:^", "openai": "^4.28.4", "react": "18.2.0", "react-dom": "18.2.0", "react-markdown": "^9.0.1", "sonner": "^1.4.3", "tailwind-merge": "^2.2.1", "tailwindcss-animate": "^1.0.7", "tippy.js": "^6.3.7", "ts-pattern": "^5.0.8", "typescript": "^5.4.2", "use-debounce": "^10.0.0", "prosemirror-state": "^1.4.3"}, "devDependencies": {"@biomejs/biome": "^1.7.2", "@types/node": "20.11.24", "@types/react": "^18.2.61", "@types/react-dom": "18.2.19", "cross-env": "^7.0.3", "tailwindcss": "^3.4.1", "tsconfig": "workspace:*"}}