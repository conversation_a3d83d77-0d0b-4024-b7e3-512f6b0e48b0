{"$schema": "../node_modules/@tauri-apps/cli/config.schema.json", "productName": "FuckNotion", "version": "0.1.0", "identifier": "com.novelpro.desktop", "build": {"frontendDist": "../web/out", "beforeDevCommand": "pnpm --filter novel-next-app dev", "beforeBuildCommand": "cd ../web && pnpm cross-env BUILD_MODE=desktop NODE_ENV=production next build && pnpm cross-env BUILD_MODE=desktop NODE_ENV=production next export -o out", "devUrl": "http://localhost:3001"}, "app": {"windows": [{"title": "FuckNotion", "width": 1280, "height": 820, "resizable": true, "fullscreen": false}], "security": {"csp": "default-src blob: data: file: http: https: ws: 'unsafe-inline' 'unsafe-eval'"}}, "bundle": {"active": true, "targets": "all", "icon": ["icons/32x32.png", "icons/128x128.png", "icons/<EMAIL>", "icons/icon.icns", "icons/icon.ico"]}}