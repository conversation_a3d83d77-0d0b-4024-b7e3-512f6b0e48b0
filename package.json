{"name": "novel", "private": true, "scripts": {"changeset": "changeset", "publish:packages": "changeset publish", "version:packages": "turbo build && changeset version", "build": "turbo build", "dev": "turbo dev", "dev:web": "turbo dev --filter=novel-next-app --filter=novel", "format": "turbo format --continue --", "format:fix": "turbo format --continue -- --write", "lint": "turbo lint --continue --", "lint:fix": "turbo lint --continue -- --write", "clean": "turbo clean", "release": "turbo run release", "prepare": "husky install", "typecheck": "turbo typecheck"}, "dependencies": {"@changesets/changelog-github": "^0.5.0", "@changesets/cli": "^2.27.11", "@tiptap/extension-table": "^2.25.0", "@tiptap/extension-table-cell": "^2.25.0", "@tiptap/extension-table-header": "^2.25.0", "@tiptap/extension-table-row": "^2.25.0", "postcss-import": "^16.1.1", "turbo": "^2.3.3"}, "packageManager": "pnpm@9.5.0", "devDependencies": {"@biomejs/biome": "^1.9.4", "@commitlint/cli": "^19.6.1", "@commitlint/config-conventional": "^19.6.0", "cross-env": "^7.0.3", "husky": "^9.1.7", "postcss": "^8.5.1"}, "commitlint": {"extends": ["@commitlint/config-conventional"], "rules": {"type-enum": [2, "always", ["build", "chore", "ci", "clean", "doc", "feat", "fix", "perf", "ref", "revert", "style", "test"]], "subject-case": [0, "always", "sentence-case"], "body-leading-blank": [2, "always", true], "body-max-line-length": [0, "always", 100]}}}