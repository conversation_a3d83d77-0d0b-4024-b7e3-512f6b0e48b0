{"permissions": {"allow": ["Bash(find:*)", "Bash(pnpm add:*)", "Bash(pnpm install:*)", "Bash(cmd /c \"pnpm install\")", "<PERSON><PERSON>(powershell:*)", "Bash(git add:*)", "Bash(git config:*)", "Bash(git commit:*)", "Bash(ls:*)", "Bash(git push:*)", "Bash(rustc:*)", "Bash(cargo --version)", "Bash(pnpm --filter novel-next-app build:desktop)", "Bash(pnpm build:desktop:*)", "Bash(npm install:*)", "Bash(rm:*)", "Bash(BUILD_MODE=desktop npm run build)", "Bash(npx tauri:*)", "Bash(cargo install:*)", "Bash(pnpm dev:web:*)", "<PERSON><PERSON>(mkdir:*)", "<PERSON><PERSON>(pkill:*)", "<PERSON><PERSON>(true)", "Bash(git tag:*)", "Bash(pnpm typecheck:*)", "<PERSON><PERSON>(curl:*)", "Bash(pnpm build:*)"], "deny": []}}