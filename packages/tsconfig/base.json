{"$schema": "https://json.schemastore.org/tsconfig", "compilerOptions": {"strict": true, "noUncheckedIndexedAccess": true, "alwaysStrict": false, "module": "ESNext", "moduleResolution": "<PERSON><PERSON><PERSON>", "resolveJsonModule": true, "target": "ESNext", "lib": ["DOM", "DOM.Iterable", "ESNext"], "noEmit": true, "declaration": true, "declarationMap": true, "verbatimModuleSyntax": true, "moduleDetection": "force", "downlevelIteration": true, "allowJs": true, "isolatedModules": true, "esModuleInterop": true, "forceConsistentCasingInFileNames": true, "skipLibCheck": true, "skipDefaultLibCheck": true, "incremental": true, "tsBuildInfoFile": ".tsbuildinfo"}, "include": ["**/*.ts", "**/*.tsx"], "exclude": ["node_modules", "src/tests"]}