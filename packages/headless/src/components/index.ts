export { useCurrentEditor as useEditor } from "@tiptap/react";
export type { Editor as EditorInstance } from "@tiptap/core";
export type { JSONContent } from "@tiptap/react";

export { EditorR<PERSON>, EditorContent, type EditorContentProps } from "./editor";
export { EditorBubble } from "./editor-bubble";
export { EditorBubbleItem } from "./editor-bubble-item";
export { Editor<PERSON><PERSON><PERSON>, EditorCommandList } from "./editor-command";
export { EditorCommandItem, EditorCommandEmpty } from "./editor-command-item";
