# [0.2.0](https://github.com/steven-tey/novel/compare/v0.1.0...v0.2.0) (2025-01-17)

## 1.0.0

### Major Changes

- cleanup novel

### Bug Fixes

- add correct config ([7c2a9a1](https://github.com/steven-tey/novel/commit/7c2a9a1eb79c774e5b11ca1f137f7f28ee4aaadf))
- add correct workflow ([db2781c](https://github.com/steven-tey/novel/commit/db2781c48e1e25517d8c473209b09a06ea2edda4))
- add crazy spinner ([256ab4a](https://github.com/steven-tey/novel/commit/256ab4a03b168ee170e90b91a90ad7b8df0e3ec6))
- add default prose ([cb93667](https://github.com/steven-tey/novel/commit/cb9366704180f9c7b5ccd793806af35d6c8f7d97))
- add docs button ([1cc5140](https://github.com/steven-tey/novel/commit/1cc514089c8ba0b1e19e26e0d72b45c4f68ea360))
- add missing dependecy ([75da619](https://github.com/steven-tey/novel/commit/75da61969b43a6f1b567c624f1a68e119578a589))
- add shorter longer options ([abdd1b7](https://github.com/steven-tey/novel/commit/abdd1b795acf1fe7211e7983171c5c73670b473a))
- build fix & prettier top level ([a8d30fc](https://github.com/steven-tey/novel/commit/a8d30fc28550fa3d1238bca0e4ec0e7a83d5ebdd))
- bump version ([74ebbc0](https://github.com/steven-tey/novel/commit/74ebbc0852deeca76668b3319e490582756c97ed))
- bump version ([2abe146](https://github.com/steven-tey/novel/commit/2abe1460207d78651593cd75990ea39195b8d5b8))
- bump version ([41d7e6a](https://github.com/steven-tey/novel/commit/41d7e6afdf787d82095bef6b819ed7572e3559f8))
- bump version ([02409d1](https://github.com/steven-tey/novel/commit/02409d14918166be52f0976a4a7f5b2df8cd4e81))
- bump version ([4294547](https://github.com/steven-tey/novel/commit/4294547e97bbd5788a4db58d8c75540cb19fc155))
- bump version ([75ed43a](https://github.com/steven-tey/novel/commit/75ed43afe643671b7249874e29e4e62053c98f40))
- bump version ([4c4b28f](https://github.com/steven-tey/novel/commit/4c4b28f981073b79a3068a8d5d1979b55cfdba20))
- cannot set text color when the text is bold ([6db8197](https://github.com/steven-tey/novel/commit/6db81970ebc2773ea723dfe60b644766b0a238dd))
- checkbox fix ([4d49c2f](https://github.com/steven-tey/novel/commit/4d49c2f92ded59c73e5e79ccbc95eed0e75f361a))
- chore bump version ([68243f6](https://github.com/steven-tey/novel/commit/68243f69613a94e414fb401992a613228f40ac98))
- chore bump version ([7051d54](https://github.com/steven-tey/novel/commit/7051d542a78320565d8ce2fe9086967603934ace))
- codeblock-lowlight doesn't render in html output ([de5c2e5](https://github.com/steven-tey/novel/commit/de5c2e55e1b7cb553ee55437d363fc0a041552c9))
- colection to collection ([50248de](https://github.com/steven-tey/novel/commit/50248dea0bb3463850f7cb1332fbc03377c17594))
- default value and css styles ([ddf0e01](https://github.com/steven-tey/novel/commit/ddf0e0145d2e41dc2ab694bf29fcc9a93d57c04e))
- **docs:** tailwind extensions guide ([7274419](https://github.com/steven-tey/novel/commit/7274419fa74a21b27f8de3db93249e90608ca6ff))
- **docs:** tailwind extensions guide ([a1457c4](https://github.com/steven-tey/novel/commit/a1457c4dfc2f869bb59e8950c49e05a50938b652))
- dont trigger slash-command on codeBlock nodes ([8a3570b](https://github.com/steven-tey/novel/commit/8a3570bd72f32076f9edb42b0492e5eea8d5a014))
- error deploy ([ca06a20](https://github.com/steven-tey/novel/commit/ca06a205fa9ffdc9a8f5b96051cc80244239989d))
- expose editor command list ([8456652](https://github.com/steven-tey/novel/commit/84566528fc3a4c3420c94f68a54a600810e9942c))
- expose utils ([33252ac](https://github.com/steven-tey/novel/commit/33252ac5148437cd4be7be736b7b8f872b07888d))
- format lint ([d4484f9](https://github.com/steven-tey/novel/commit/d4484f96b764f17ac9bcb152db7caa5279ca7666))
- image is deleted if an error occurs ([13e74b3](https://github.com/steven-tey/novel/commit/13e74b3a4b9b77356c895892f0a142d3a872403e))
- keep drag handle ([8363ea6](https://github.com/steven-tey/novel/commit/8363ea60c6bc7bd64c5e8409b38a8696d1d7240f))
- not show bubble menu if editor cannot be editable ([2aeee1b](https://github.com/steven-tey/novel/commit/2aeee1b1f402cd2006a9f86b0450efb8db003e73))
- pass ref to div ([a3cd338](https://github.com/steven-tey/novel/commit/a3cd33888ab47a86a80f000a35b8abd6c663e431))
- remove AI autocomplete default placeholder ([5ca260e](https://github.com/steven-tey/novel/commit/5ca260e4496ec9e4da114cb41ee874090a519c13))
- remove auto joiner ([dbef03c](https://github.com/steven-tey/novel/commit/dbef03c5cafb691c3dc4f8c14e6944707ca70a98))
- remove default extensions and make them standalone exports ([54bfd40](https://github.com/steven-tey/novel/commit/54bfd404b013a094449a2ac16ec47a561ddd15a5))
- remove drag-handle on drop ([1ec5518](https://github.com/steven-tey/novel/commit/1ec551819e2490afddd1e042e0eb70d626c3325d))
- remove katex styling import inside mathematics extension ([91264c7](https://github.com/steven-tey/novel/commit/91264c7c764f8a1b0d3859c3179fc8ccf18f330f))
- rename to workflows ([16d3ff5](https://github.com/steven-tey/novel/commit/16d3ff56a6b1cb291b79c72dcf71ed5735645dcc))
- rename updated image type ([d1b21d6](https://github.com/steven-tey/novel/commit/d1b21d695ba105e3e7f26d607ee8969b92289ff5))
- safari related fix ([40a6fd8](https://github.com/steven-tey/novel/commit/40a6fd8ef3d881a89792e2c078c4f56bde4327ec))
- The tailwind example link on setup page redirects to correct file ([55e4e69](https://github.com/steven-tey/novel/commit/55e4e69da6059023716e08bbb40c87cf829c9588))
- update docs & rename type to EditorInstance ([ff9cf90](https://github.com/steven-tey/novel/commit/ff9cf902581cc2a65167f2b5493c9c183a489817))
- update packge docs ([40b860f](https://github.com/steven-tey/novel/commit/40b860f654483934846fe66991c13906400c0fb1))
- update tiptap-markdown ([d6358fe](https://github.com/steven-tey/novel/commit/d6358fe2c036e2d4c8101abb0912c8faabc9cf60))
- update types ([8d168f5](https://github.com/steven-tey/novel/commit/8d168f58ca8e6cc9a859f232a570a2ded6532367))
- use per-editor instance of tunnel to render slash command popover ([d05c03f](https://github.com/steven-tey/novel/commit/d05c03ff5f668d8b65a1729a9adc2d79315f9591))
- use verbatim import ([1c4df17](https://github.com/steven-tey/novel/commit/1c4df17f252773ca472150541468a01c18d37588))

### Features

- add ai features example ([2a5e18c](https://github.com/steven-tey/novel/commit/2a5e18c8950f2e26659775d812b1be1d56baf169))
- add custom highlight extension ([45efd37](https://github.com/steven-tey/novel/commit/45efd37d157e5f0654fb8ec83e5175df7b2aa918))
- add custom upload config ([a202e6e](https://github.com/steven-tey/novel/commit/a202e6eb14488fe640e082d9fa665ce32ff02f65))
- add dialog usage ([9152e46](https://github.com/steven-tey/novel/commit/9152e461a5ba8fb6480d423b4688a15407365c47))
- add docs step to include editor props ([17dcc6d](https://github.com/steven-tey/novel/commit/17dcc6d94a9d213f86a704c043985b97138912c1))
- add issue template ([2241fd1](https://github.com/steven-tey/novel/commit/2241fd1c5275456f8cd81cffb19dbe9055444bab))
- add mathematics extension ([15b4428](https://github.com/steven-tey/novel/commit/15b44284d60a7ee88da3e1f1ee3462acdc1f8af8))
- add twitter extension ([e019f34](https://github.com/steven-tey/novel/commit/e019f34575b0a9d8a1e1fbc04c72c100780ed035))
- add utils functions for text generation ([7e99b72](https://github.com/steven-tey/novel/commit/7e99b722e393cde51bd206b0d012c130837c7137))
- ai prev markdown ([122b3ee](https://github.com/steven-tey/novel/commit/122b3eed4e748e0824b6a7d670bf26c93bdada5a))
- clear nodes on node selector ([596d811](https://github.com/steven-tey/novel/commit/596d81176030b29dfa1b41ee99797e855e9cafbe))
- configure changeset for release ([c09dd55](https://github.com/steven-tey/novel/commit/c09dd55f0cc271b8d272a03a14a8b6108f611ee5))
- fix biome linting ([081ab3b](https://github.com/steven-tey/novel/commit/081ab3bd6367d6e2e5660da1f2615ce322def85c))
- forward ref components ([957e5dc](https://github.com/steven-tey/novel/commit/957e5dc279c804bab4c4af8a94f9275967fbbc65))
- remove old docs & example add typecheck ([e0b2c99](https://github.com/steven-tey/novel/commit/e0b2c99b913fb283d5b55d94a0837b986936e160))
- support for custom OpenAI base url ([7ac5895](https://github.com/steven-tey/novel/commit/7ac5895b7aece309a1e671bf5fa4d5042db296ea))
- update docs ([9534c6e](https://github.com/steven-tey/novel/commit/9534c6ed78fc5850e46673499117fc144c770058))
- update docs with demo code link ([4569347](https://github.com/steven-tey/novel/commit/4569347e8306517747aa0b5be40c399a286b1b9a))
- use biome for linting & formatting ([e2601a0](https://github.com/steven-tey/novel/commit/e2601a059332e7db580d517f3081d7db555a1fb3))
- use semantic release library ([4854d8a](https://github.com/steven-tey/novel/commit/4854d8a4a1d315dfbd3d96ca9e9a91e4f08afbfe))

# [0.2.0](https://github.com/steven-tey/novel/compare/v0.1.0...v0.2.0) (2025-01-17)

### Bug Fixes

- add correct config ([7c2a9a1](https://github.com/steven-tey/novel/commit/7c2a9a1eb79c774e5b11ca1f137f7f28ee4aaadf))
- add correct workflow ([db2781c](https://github.com/steven-tey/novel/commit/db2781c48e1e25517d8c473209b09a06ea2edda4))
- add crazy spinner ([256ab4a](https://github.com/steven-tey/novel/commit/256ab4a03b168ee170e90b91a90ad7b8df0e3ec6))
- add default prose ([cb93667](https://github.com/steven-tey/novel/commit/cb9366704180f9c7b5ccd793806af35d6c8f7d97))
- add docs button ([1cc5140](https://github.com/steven-tey/novel/commit/1cc514089c8ba0b1e19e26e0d72b45c4f68ea360))
- add missing dependecy ([75da619](https://github.com/steven-tey/novel/commit/75da61969b43a6f1b567c624f1a68e119578a589))
- add shorter longer options ([abdd1b7](https://github.com/steven-tey/novel/commit/abdd1b795acf1fe7211e7983171c5c73670b473a))
- build fix & prettier top level ([a8d30fc](https://github.com/steven-tey/novel/commit/a8d30fc28550fa3d1238bca0e4ec0e7a83d5ebdd))
- bump version ([74ebbc0](https://github.com/steven-tey/novel/commit/74ebbc0852deeca76668b3319e490582756c97ed))
- bump version ([2abe146](https://github.com/steven-tey/novel/commit/2abe1460207d78651593cd75990ea39195b8d5b8))
- bump version ([41d7e6a](https://github.com/steven-tey/novel/commit/41d7e6afdf787d82095bef6b819ed7572e3559f8))
- bump version ([02409d1](https://github.com/steven-tey/novel/commit/02409d14918166be52f0976a4a7f5b2df8cd4e81))
- bump version ([4294547](https://github.com/steven-tey/novel/commit/4294547e97bbd5788a4db58d8c75540cb19fc155))
- bump version ([75ed43a](https://github.com/steven-tey/novel/commit/75ed43afe643671b7249874e29e4e62053c98f40))
- bump version ([4c4b28f](https://github.com/steven-tey/novel/commit/4c4b28f981073b79a3068a8d5d1979b55cfdba20))
- cannot set text color when the text is bold ([6db8197](https://github.com/steven-tey/novel/commit/6db81970ebc2773ea723dfe60b644766b0a238dd))
- checkbox fix ([4d49c2f](https://github.com/steven-tey/novel/commit/4d49c2f92ded59c73e5e79ccbc95eed0e75f361a))
- chore bump version ([68243f6](https://github.com/steven-tey/novel/commit/68243f69613a94e414fb401992a613228f40ac98))
- chore bump version ([7051d54](https://github.com/steven-tey/novel/commit/7051d542a78320565d8ce2fe9086967603934ace))
- codeblock-lowlight doesn't render in html output ([de5c2e5](https://github.com/steven-tey/novel/commit/de5c2e55e1b7cb553ee55437d363fc0a041552c9))
- colection to collection ([50248de](https://github.com/steven-tey/novel/commit/50248dea0bb3463850f7cb1332fbc03377c17594))
- default value and css styles ([ddf0e01](https://github.com/steven-tey/novel/commit/ddf0e0145d2e41dc2ab694bf29fcc9a93d57c04e))
- **docs:** tailwind extensions guide ([7274419](https://github.com/steven-tey/novel/commit/7274419fa74a21b27f8de3db93249e90608ca6ff))
- **docs:** tailwind extensions guide ([a1457c4](https://github.com/steven-tey/novel/commit/a1457c4dfc2f869bb59e8950c49e05a50938b652))
- dont trigger slash-command on codeBlock nodes ([8a3570b](https://github.com/steven-tey/novel/commit/8a3570bd72f32076f9edb42b0492e5eea8d5a014))
- error deploy ([ca06a20](https://github.com/steven-tey/novel/commit/ca06a205fa9ffdc9a8f5b96051cc80244239989d))
- expose editor command list ([8456652](https://github.com/steven-tey/novel/commit/84566528fc3a4c3420c94f68a54a600810e9942c))
- expose utils ([33252ac](https://github.com/steven-tey/novel/commit/33252ac5148437cd4be7be736b7b8f872b07888d))
- format lint ([d4484f9](https://github.com/steven-tey/novel/commit/d4484f96b764f17ac9bcb152db7caa5279ca7666))
- image is deleted if an error occurs ([13e74b3](https://github.com/steven-tey/novel/commit/13e74b3a4b9b77356c895892f0a142d3a872403e))
- keep drag handle ([8363ea6](https://github.com/steven-tey/novel/commit/8363ea60c6bc7bd64c5e8409b38a8696d1d7240f))
- not show bubble menu if editor cannot be editable ([2aeee1b](https://github.com/steven-tey/novel/commit/2aeee1b1f402cd2006a9f86b0450efb8db003e73))
- pass ref to div ([a3cd338](https://github.com/steven-tey/novel/commit/a3cd33888ab47a86a80f000a35b8abd6c663e431))
- remove AI autocomplete default placeholder ([5ca260e](https://github.com/steven-tey/novel/commit/5ca260e4496ec9e4da114cb41ee874090a519c13))
- remove auto joiner ([dbef03c](https://github.com/steven-tey/novel/commit/dbef03c5cafb691c3dc4f8c14e6944707ca70a98))
- remove default extensions and make them standalone exports ([54bfd40](https://github.com/steven-tey/novel/commit/54bfd404b013a094449a2ac16ec47a561ddd15a5))
- remove drag-handle on drop ([1ec5518](https://github.com/steven-tey/novel/commit/1ec551819e2490afddd1e042e0eb70d626c3325d))
- remove katex styling import inside mathematics extension ([91264c7](https://github.com/steven-tey/novel/commit/91264c7c764f8a1b0d3859c3179fc8ccf18f330f))
- rename to workflows ([16d3ff5](https://github.com/steven-tey/novel/commit/16d3ff56a6b1cb291b79c72dcf71ed5735645dcc))
- rename updated image type ([d1b21d6](https://github.com/steven-tey/novel/commit/d1b21d695ba105e3e7f26d607ee8969b92289ff5))
- safari related fix ([40a6fd8](https://github.com/steven-tey/novel/commit/40a6fd8ef3d881a89792e2c078c4f56bde4327ec))
- The tailwind example link on setup page redirects to correct file ([55e4e69](https://github.com/steven-tey/novel/commit/55e4e69da6059023716e08bbb40c87cf829c9588))
- update docs & rename type to EditorInstance ([ff9cf90](https://github.com/steven-tey/novel/commit/ff9cf902581cc2a65167f2b5493c9c183a489817))
- update packge docs ([40b860f](https://github.com/steven-tey/novel/commit/40b860f654483934846fe66991c13906400c0fb1))
- update tiptap-markdown ([d6358fe](https://github.com/steven-tey/novel/commit/d6358fe2c036e2d4c8101abb0912c8faabc9cf60))
- update types ([8d168f5](https://github.com/steven-tey/novel/commit/8d168f58ca8e6cc9a859f232a570a2ded6532367))
- use per-editor instance of tunnel to render slash command popover ([d05c03f](https://github.com/steven-tey/novel/commit/d05c03ff5f668d8b65a1729a9adc2d79315f9591))
- use verbatim import ([1c4df17](https://github.com/steven-tey/novel/commit/1c4df17f252773ca472150541468a01c18d37588))

### Features

- add ai features example ([2a5e18c](https://github.com/steven-tey/novel/commit/2a5e18c8950f2e26659775d812b1be1d56baf169))
- add custom highlight extension ([45efd37](https://github.com/steven-tey/novel/commit/45efd37d157e5f0654fb8ec83e5175df7b2aa918))
- add custom upload config ([a202e6e](https://github.com/steven-tey/novel/commit/a202e6eb14488fe640e082d9fa665ce32ff02f65))
- add dialog usage ([9152e46](https://github.com/steven-tey/novel/commit/9152e461a5ba8fb6480d423b4688a15407365c47))
- add docs step to include editor props ([17dcc6d](https://github.com/steven-tey/novel/commit/17dcc6d94a9d213f86a704c043985b97138912c1))
- add issue template ([2241fd1](https://github.com/steven-tey/novel/commit/2241fd1c5275456f8cd81cffb19dbe9055444bab))
- add mathematics extension ([15b4428](https://github.com/steven-tey/novel/commit/15b44284d60a7ee88da3e1f1ee3462acdc1f8af8))
- add twitter extension ([e019f34](https://github.com/steven-tey/novel/commit/e019f34575b0a9d8a1e1fbc04c72c100780ed035))
- add utils functions for text generation ([7e99b72](https://github.com/steven-tey/novel/commit/7e99b722e393cde51bd206b0d012c130837c7137))
- ai prev markdown ([122b3ee](https://github.com/steven-tey/novel/commit/122b3eed4e748e0824b6a7d670bf26c93bdada5a))
- clear nodes on node selector ([596d811](https://github.com/steven-tey/novel/commit/596d81176030b29dfa1b41ee99797e855e9cafbe))
- configure changeset for release ([c09dd55](https://github.com/steven-tey/novel/commit/c09dd55f0cc271b8d272a03a14a8b6108f611ee5))
- fix biome linting ([081ab3b](https://github.com/steven-tey/novel/commit/081ab3bd6367d6e2e5660da1f2615ce322def85c))
- forward ref components ([957e5dc](https://github.com/steven-tey/novel/commit/957e5dc279c804bab4c4af8a94f9275967fbbc65))
- remove old docs & example add typecheck ([e0b2c99](https://github.com/steven-tey/novel/commit/e0b2c99b913fb283d5b55d94a0837b986936e160))
- support for custom OpenAI base url ([7ac5895](https://github.com/steven-tey/novel/commit/7ac5895b7aece309a1e671bf5fa4d5042db296ea))
- update docs ([9534c6e](https://github.com/steven-tey/novel/commit/9534c6ed78fc5850e46673499117fc144c770058))
- update docs with demo code link ([4569347](https://github.com/steven-tey/novel/commit/4569347e8306517747aa0b5be40c399a286b1b9a))
- use biome for linting & formatting ([e2601a0](https://github.com/steven-tey/novel/commit/e2601a059332e7db580d517f3081d7db555a1fb3))
- use semantic release library ([4854d8a](https://github.com/steven-tey/novel/commit/4854d8a4a1d315dfbd3d96ca9e9a91e4f08afbfe))

# [0.2.0](https://github.com/steven-tey/novel/compare/v0.1.0...v0.2.0) (2025-01-17)

### Bug Fixes

- add correct config ([7c2a9a1](https://github.com/steven-tey/novel/commit/7c2a9a1eb79c774e5b11ca1f137f7f28ee4aaadf))
- add correct workflow ([db2781c](https://github.com/steven-tey/novel/commit/db2781c48e1e25517d8c473209b09a06ea2edda4))
- add crazy spinner ([256ab4a](https://github.com/steven-tey/novel/commit/256ab4a03b168ee170e90b91a90ad7b8df0e3ec6))
- add default prose ([cb93667](https://github.com/steven-tey/novel/commit/cb9366704180f9c7b5ccd793806af35d6c8f7d97))
- add docs button ([1cc5140](https://github.com/steven-tey/novel/commit/1cc514089c8ba0b1e19e26e0d72b45c4f68ea360))
- add missing dependecy ([75da619](https://github.com/steven-tey/novel/commit/75da61969b43a6f1b567c624f1a68e119578a589))
- add shorter longer options ([abdd1b7](https://github.com/steven-tey/novel/commit/abdd1b795acf1fe7211e7983171c5c73670b473a))
- build fix & prettier top level ([a8d30fc](https://github.com/steven-tey/novel/commit/a8d30fc28550fa3d1238bca0e4ec0e7a83d5ebdd))
- bump version ([74ebbc0](https://github.com/steven-tey/novel/commit/74ebbc0852deeca76668b3319e490582756c97ed))
- bump version ([2abe146](https://github.com/steven-tey/novel/commit/2abe1460207d78651593cd75990ea39195b8d5b8))
- bump version ([41d7e6a](https://github.com/steven-tey/novel/commit/41d7e6afdf787d82095bef6b819ed7572e3559f8))
- bump version ([02409d1](https://github.com/steven-tey/novel/commit/02409d14918166be52f0976a4a7f5b2df8cd4e81))
- bump version ([4294547](https://github.com/steven-tey/novel/commit/4294547e97bbd5788a4db58d8c75540cb19fc155))
- bump version ([75ed43a](https://github.com/steven-tey/novel/commit/75ed43afe643671b7249874e29e4e62053c98f40))
- bump version ([4c4b28f](https://github.com/steven-tey/novel/commit/4c4b28f981073b79a3068a8d5d1979b55cfdba20))
- cannot set text color when the text is bold ([6db8197](https://github.com/steven-tey/novel/commit/6db81970ebc2773ea723dfe60b644766b0a238dd))
- checkbox fix ([4d49c2f](https://github.com/steven-tey/novel/commit/4d49c2f92ded59c73e5e79ccbc95eed0e75f361a))
- chore bump version ([68243f6](https://github.com/steven-tey/novel/commit/68243f69613a94e414fb401992a613228f40ac98))
- chore bump version ([7051d54](https://github.com/steven-tey/novel/commit/7051d542a78320565d8ce2fe9086967603934ace))
- codeblock-lowlight doesn't render in html output ([de5c2e5](https://github.com/steven-tey/novel/commit/de5c2e55e1b7cb553ee55437d363fc0a041552c9))
- colection to collection ([50248de](https://github.com/steven-tey/novel/commit/50248dea0bb3463850f7cb1332fbc03377c17594))
- default value and css styles ([ddf0e01](https://github.com/steven-tey/novel/commit/ddf0e0145d2e41dc2ab694bf29fcc9a93d57c04e))
- **docs:** tailwind extensions guide ([7274419](https://github.com/steven-tey/novel/commit/7274419fa74a21b27f8de3db93249e90608ca6ff))
- **docs:** tailwind extensions guide ([a1457c4](https://github.com/steven-tey/novel/commit/a1457c4dfc2f869bb59e8950c49e05a50938b652))
- dont trigger slash-command on codeBlock nodes ([8a3570b](https://github.com/steven-tey/novel/commit/8a3570bd72f32076f9edb42b0492e5eea8d5a014))
- error deploy ([ca06a20](https://github.com/steven-tey/novel/commit/ca06a205fa9ffdc9a8f5b96051cc80244239989d))
- expose editor command list ([8456652](https://github.com/steven-tey/novel/commit/84566528fc3a4c3420c94f68a54a600810e9942c))
- expose utils ([33252ac](https://github.com/steven-tey/novel/commit/33252ac5148437cd4be7be736b7b8f872b07888d))
- format lint ([d4484f9](https://github.com/steven-tey/novel/commit/d4484f96b764f17ac9bcb152db7caa5279ca7666))
- image is deleted if an error occurs ([13e74b3](https://github.com/steven-tey/novel/commit/13e74b3a4b9b77356c895892f0a142d3a872403e))
- keep drag handle ([8363ea6](https://github.com/steven-tey/novel/commit/8363ea60c6bc7bd64c5e8409b38a8696d1d7240f))
- not show bubble menu if editor cannot be editable ([2aeee1b](https://github.com/steven-tey/novel/commit/2aeee1b1f402cd2006a9f86b0450efb8db003e73))
- pass ref to div ([a3cd338](https://github.com/steven-tey/novel/commit/a3cd33888ab47a86a80f000a35b8abd6c663e431))
- remove AI autocomplete default placeholder ([5ca260e](https://github.com/steven-tey/novel/commit/5ca260e4496ec9e4da114cb41ee874090a519c13))
- remove auto joiner ([dbef03c](https://github.com/steven-tey/novel/commit/dbef03c5cafb691c3dc4f8c14e6944707ca70a98))
- remove default extensions and make them standalone exports ([54bfd40](https://github.com/steven-tey/novel/commit/54bfd404b013a094449a2ac16ec47a561ddd15a5))
- remove drag-handle on drop ([1ec5518](https://github.com/steven-tey/novel/commit/1ec551819e2490afddd1e042e0eb70d626c3325d))
- remove katex styling import inside mathematics extension ([91264c7](https://github.com/steven-tey/novel/commit/91264c7c764f8a1b0d3859c3179fc8ccf18f330f))
- rename to workflows ([16d3ff5](https://github.com/steven-tey/novel/commit/16d3ff56a6b1cb291b79c72dcf71ed5735645dcc))
- rename updated image type ([d1b21d6](https://github.com/steven-tey/novel/commit/d1b21d695ba105e3e7f26d607ee8969b92289ff5))
- safari related fix ([40a6fd8](https://github.com/steven-tey/novel/commit/40a6fd8ef3d881a89792e2c078c4f56bde4327ec))
- The tailwind example link on setup page redirects to correct file ([55e4e69](https://github.com/steven-tey/novel/commit/55e4e69da6059023716e08bbb40c87cf829c9588))
- update docs & rename type to EditorInstance ([ff9cf90](https://github.com/steven-tey/novel/commit/ff9cf902581cc2a65167f2b5493c9c183a489817))
- update packge docs ([40b860f](https://github.com/steven-tey/novel/commit/40b860f654483934846fe66991c13906400c0fb1))
- update tiptap-markdown ([d6358fe](https://github.com/steven-tey/novel/commit/d6358fe2c036e2d4c8101abb0912c8faabc9cf60))
- update types ([8d168f5](https://github.com/steven-tey/novel/commit/8d168f58ca8e6cc9a859f232a570a2ded6532367))
- use per-editor instance of tunnel to render slash command popover ([d05c03f](https://github.com/steven-tey/novel/commit/d05c03ff5f668d8b65a1729a9adc2d79315f9591))
- use verbatim import ([1c4df17](https://github.com/steven-tey/novel/commit/1c4df17f252773ca472150541468a01c18d37588))

### Features

- add ai features example ([2a5e18c](https://github.com/steven-tey/novel/commit/2a5e18c8950f2e26659775d812b1be1d56baf169))
- add custom highlight extension ([45efd37](https://github.com/steven-tey/novel/commit/45efd37d157e5f0654fb8ec83e5175df7b2aa918))
- add custom upload config ([a202e6e](https://github.com/steven-tey/novel/commit/a202e6eb14488fe640e082d9fa665ce32ff02f65))
- add dialog usage ([9152e46](https://github.com/steven-tey/novel/commit/9152e461a5ba8fb6480d423b4688a15407365c47))
- add docs step to include editor props ([17dcc6d](https://github.com/steven-tey/novel/commit/17dcc6d94a9d213f86a704c043985b97138912c1))
- add issue template ([2241fd1](https://github.com/steven-tey/novel/commit/2241fd1c5275456f8cd81cffb19dbe9055444bab))
- add mathematics extension ([15b4428](https://github.com/steven-tey/novel/commit/15b44284d60a7ee88da3e1f1ee3462acdc1f8af8))
- add twitter extension ([e019f34](https://github.com/steven-tey/novel/commit/e019f34575b0a9d8a1e1fbc04c72c100780ed035))
- add utils functions for text generation ([7e99b72](https://github.com/steven-tey/novel/commit/7e99b722e393cde51bd206b0d012c130837c7137))
- ai prev markdown ([122b3ee](https://github.com/steven-tey/novel/commit/122b3eed4e748e0824b6a7d670bf26c93bdada5a))
- clear nodes on node selector ([596d811](https://github.com/steven-tey/novel/commit/596d81176030b29dfa1b41ee99797e855e9cafbe))
- configure changeset for release ([c09dd55](https://github.com/steven-tey/novel/commit/c09dd55f0cc271b8d272a03a14a8b6108f611ee5))
- fix biome linting ([081ab3b](https://github.com/steven-tey/novel/commit/081ab3bd6367d6e2e5660da1f2615ce322def85c))
- forward ref components ([957e5dc](https://github.com/steven-tey/novel/commit/957e5dc279c804bab4c4af8a94f9275967fbbc65))
- remove old docs & example add typecheck ([e0b2c99](https://github.com/steven-tey/novel/commit/e0b2c99b913fb283d5b55d94a0837b986936e160))
- support for custom OpenAI base url ([7ac5895](https://github.com/steven-tey/novel/commit/7ac5895b7aece309a1e671bf5fa4d5042db296ea))
- update docs ([9534c6e](https://github.com/steven-tey/novel/commit/9534c6ed78fc5850e46673499117fc144c770058))
- update docs with demo code link ([4569347](https://github.com/steven-tey/novel/commit/4569347e8306517747aa0b5be40c399a286b1b9a))
- use biome for linting & formatting ([e2601a0](https://github.com/steven-tey/novel/commit/e2601a059332e7db580d517f3081d7db555a1fb3))
- use semantic release library ([4854d8a](https://github.com/steven-tey/novel/commit/4854d8a4a1d315dfbd3d96ca9e9a91e4f08afbfe))

# novel

## 0.5.0

### Minor Changes

- update extensions export

## 0.4.3

### Patch Changes

- add twitter extension

## 0.4.2

### Patch Changes

- bump version

## 0.4.1

### Patch Changes

- expose utils

## 0.4.0

### Minor Changes

- expose utils fix bugs

## 0.3.1

### Patch Changes

- regression fix

## 0.3.0

### Minor Changes

- update drag handle

## 0.2.13

### Patch Changes

- small fixes

## 0.2.12

### Patch Changes

- Expose command list editor

## 0.2.11

### Patch Changes

- Ai utils & generative example

## 0.2.10

### Patch Changes

- Fix types

## 0.2.9

### Patch Changes

- Custom upload config

## 0.2.8

### Patch Changes

- Code quality and extensions fixing

## 0.2.7

### Patch Changes

- [#311](https://github.com/steven-tey/novel/pull/311) [`c09dd55`](https://github.com/steven-tey/novel/commit/c09dd55f0cc271b8d272a03a14a8b6108f611ee5) Thanks [@andrewdoro](https://github.com/andrewdoro)! - Rename type from Editor to EditorInstance
