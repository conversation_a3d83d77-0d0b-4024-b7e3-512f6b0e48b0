{"name": "novel", "version": "1.0.0", "description": "Notion-style WYSIWYG editor with AI-powered autocompletions", "license": "Apache-2.0", "type": "module", "main": "dist/index.cjs", "module": "dist/index.js", "types": "dist/index.d.ts", "files": ["dist"], "scripts": {"dev": "tsup --watch", "typecheck": "tsc --noEmit", "build": "tsup", "lint": "biome lint ./src", "format": "biome format ./src "}, "sideEffects": false, "peerDependencies": {"react": ">=18"}, "dependencies": {"@radix-ui/react-slot": "^1.1.1", "@tiptap/core": "^2.25.0", "@tiptap/extension-character-count": "^2.25.0", "@tiptap/extension-code-block-lowlight": "^2.25.0", "@tiptap/extension-color": "^2.25.0", "@tiptap/extension-highlight": "^2.25.0", "@tiptap/extension-horizontal-rule": "^2.25.0", "@tiptap/extension-image": "^2.25.0", "@tiptap/extension-link": "^2.25.0", "@tiptap/extension-placeholder": "^2.25.0", "@tiptap/extension-task-item": "^2.25.0", "@tiptap/extension-task-list": "^2.25.0", "@tiptap/extension-text-style": "^2.25.0", "@tiptap/extension-underline": "^2.25.0", "@tiptap/extension-youtube": "^2.25.0", "@tiptap/pm": "^2.25.0", "@tiptap/react": "^2.25.0", "prosemirror-state": "^1.4.3", "@tiptap/starter-kit": "^2.25.0", "@tiptap/suggestion": "^2.25.0", "@types/node": "^22.10.6", "cmdk": "^1.0.4", "jotai": "^2.11.0", "react-markdown": "^9.0.3", "react-moveable": "^0.56.0", "react-tweet": "^3.2.1", "katex": "^0.16.20", "tippy.js": "^6.3.7", "tiptap-extension-global-drag-handle": "^0.1.16", "tiptap-markdown": "^0.8.10", "tunnel-rat": "^0.1.2"}, "devDependencies": {"@biomejs/biome": "^1.9.4", "@types/katex": "^0.16.7", "@types/react": "^18.2.55", "@types/react-dom": "18.2.19", "tsconfig": "workspace:*", "tsup": "^8.3.5", "typescript": "^5.7.3"}, "author": "<PERSON> <<EMAIL>>", "homepage": "https://novel.sh", "repository": {"type": "git", "url": "git+https://github.com/steven-tey/novel.git"}, "bugs": {"url": "https://github.com/steven-tey/novel/issues"}, "keywords": ["ai", "novel", "editor", "markdown", "nextjs", "react"]}