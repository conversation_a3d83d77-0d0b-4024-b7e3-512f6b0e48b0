# Recommended Autocomplete Settings

## For Best Performance:

### Fast & Responsive Settings:
- **Text Length**: 30-50 tokens (shorter = faster)
- **Delay**: 20-50ms (balance between responsiveness and not being too aggressive)
- **Min Characters**: 3-5 (so autocomplete triggers after just a few characters)

### Your Current Settings Issue:
- **Min Characters = 20**: This is too high! You need to type 20+ characters before seeing any suggestions
- **Text Length = 240**: Too long, causes slower generation

## Recommended Configuration:
1. Set **Min Characters** to **3**
2. Set **Text Length** to **50**
3. Set **Delay** to **20**

This will give you:
- Autocomplete triggers after just 3 characters
- Fast, short completions
- Responsive feel with minimal delay

## To Apply:
1. Go to /settings
2. Update the values:
   - Text Length: 50
   - Delay: 20
   - Min Characters: 3
3. Click "Save & Test"
4. Return to editor and try typing