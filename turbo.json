{"$schema": "https://turbo.build/schema.json", "globalDependencies": ["**/.env.*local"], "tasks": {"topo": {"dependsOn": ["^topo"]}, "build": {"dependsOn": ["^build", "typecheck"], "outputs": ["dist/**", ".next/**", "!.next/cache/**"]}, "typecheck": {"dependsOn": ["^topo"], "outputs": []}, "lint": {"dependsOn": ["^topo"]}, "format": {"dependsOn": ["^topo"]}, "lint:fix": {"dependsOn": ["^topo"]}, "format:fix": {"dependsOn": ["^topo"]}, "check-types": {}, "dev": {"cache": false, "persistent": true}, "clean": {"cache": false}, "release": {"cache": false}, "desktop#build": {"dependsOn": ["novel-next-app#build:desktop", "novel-next-app#export"], "outputs": ["apps/desktop/src-tauri/target/**"]}, "desktop#dev": {"cache": false}}}